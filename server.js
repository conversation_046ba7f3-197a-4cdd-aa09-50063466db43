const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const config = require('./config/config');

const app = express();

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 确保音频输出目录存在
fs.ensureDirSync(config.audio.outputDir);

// API路由
app.use('/api/tts', require('./src/routes/tts'));

// 根路由 - 提供主页面
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: config.server.env === 'development' ? err.message : '服务器错误'
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: '请求的资源不存在'
  });
});

// 启动服务器
const PORT = config.server.port;
app.listen(PORT, () => {
  console.log(`🎙️  文本转语音系统启动成功！`);
  console.log(`🌐 服务器地址: http://localhost:${PORT}`);
  console.log(`📁 音频输出目录: ${config.audio.outputDir}`);
  console.log(`🎵 支持音色数量: ${config.voices.length}个 (包含所有分类)`);
  console.log(`🚀 环境: ${config.server.env}`);
});

module.exports = app;