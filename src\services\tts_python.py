#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音合成Python脚本
使用阿里云DashScope CosyVoice Python SDK
"""

import os
import sys
import json
import argparse
import dashscope
from dashscope.audio.tts_v2 import *

def setup_api_key():
    """设置API密钥"""
    # 从环境变量获取API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        # 如果环境变量没有，尝试从配置文件读取
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/config.js')
            # 这里简化处理，实际项目中可能需要更复杂的配置读取
            # 建议在环境变量中设置API密钥
            pass
        except:
            pass
    
    if api_key:
        dashscope.api_key = api_key
        return True
    return False

def synthesize_speech(text, voice='longxiaochun_v2', format='mp3', sample_rate=22050, 
                     volume=50, rate=1.0, pitch=1.0, output_file=None):
    """语音合成函数"""
    try:
        # 设置API密钥
        if not setup_api_key():
            raise Exception('未配置DASHSCOPE_API_KEY环境变量')
        
        # 清理和验证文本
        text = text.strip()
        if not text:
            raise Exception('文本内容为空')
        
        # 移除可能导致问题的特殊字符
        import re
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
        
        # 验证文本长度
        char_count = 0
        for char in text:
            if '\u4e00' <= char <= '\u9fa5':  # 汉字
                char_count += 2
            else:  # 英文字母/数字/标点/空格
                char_count += 1
        
        if char_count > 2000:
            raise Exception('文本长度超过限制（2000字符）')
        
        print(f"处理后的文本: {repr(text)}")
        
        # 根据音色选择合适的模型
        # v2音色使用cosyvoice-v2模型，其他音色使用cosyvoice-v1模型
        v2_voices = ['longxiaochun_v2', 'yumi_v2', 'longxiu_v2', 'longsanshu']
        if voice in v2_voices:
            model = "cosyvoice-v2"
        else:
            model = "cosyvoice-v1"
        
        print(f"使用模型: {model}, 音色: {voice}")
        
        # 创建语音合成器
        synthesizer = SpeechSynthesizer(
            model=model,
            voice=voice
        )
        
        # 进行语音合成
        audio_data = synthesizer.call(text)
        
        if audio_data is None:
            raise Exception('语音合成失败，返回数据为空')
        
        # 保存音频文件
        if output_file:
            with open(output_file, 'wb') as f:
                f.write(audio_data)
            
            return {
                'success': True,
                'message': '语音合成成功',
                'file_path': output_file,
                'file_size': len(audio_data),
                'request_id': synthesizer.get_last_request_id(),
                'first_package_delay': synthesizer.get_first_package_delay()
            }
        else:
            return {
                'success': True,
                'message': '语音合成成功',
                'audio_data': audio_data.hex(),  # 转换为十六进制字符串
                'file_size': len(audio_data),
                'request_id': synthesizer.get_last_request_id(),
                'first_package_delay': synthesizer.get_first_package_delay()
            }
            
    except Exception as e:
        return {
            'success': False,
            'message': f'语音合成失败: {str(e)}',
            'error': str(e)
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='语音合成工具')
    parser.add_argument('--voice', default='longxiaochun_v2', help='音色')
    parser.add_argument('--format', default='mp3', help='音频格式')
    parser.add_argument('--sample-rate', type=int, default=22050, help='采样率')
    parser.add_argument('--volume', type=int, default=50, help='音量 (0-100)')
    parser.add_argument('--rate', type=float, default=1.0, help='语速')
    parser.add_argument('--pitch', type=float, default=1.0, help='音调')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--input-file', help='输入文本的JSON文件路径')
    
    args = parser.parse_args()
    
    # 从stdin读取JSON数据
    try:
        # 从文件或stdin读取JSON数据
        if args.input_file:
            # 从文件读取
            with open(args.input_file, 'r', encoding='utf-8') as f:
                input_json = json.load(f)
                text = input_json.get('text', '')
                print(f"从文件读取的文本: {repr(text)}")
        else:
            # 从stdin读取（保持向后兼容）
            stdin_data = sys.stdin.buffer.read()
            print(f"原始字节数据: {stdin_data}")
            
            # 尝试不同的编码方式
            for encoding in ['utf-8', 'gbk', 'cp936']:
                try:
                    decoded_data = stdin_data.decode(encoding).strip()
                    print(f"解码后数据 ({encoding}): {repr(decoded_data)}")
                    if not decoded_data:
                        print(json.dumps({'success': False, 'message': '未提供文本内容'}, ensure_ascii=False, indent=2))
                        sys.exit(1)
                    
                    input_json = json.loads(decoded_data)
                    text = input_json.get('text', '')
                    print(f"提取的文本: {repr(text)}")
                    print(f"成功使用编码: {encoding}")
                    break
                except (UnicodeDecodeError, json.JSONDecodeError) as e:
                    print(f"编码 {encoding} 失败: {e}")
                    continue
            else:
                raise Exception('无法解码输入数据')
        
        if not text:
            print(json.dumps({'success': False, 'message': '文本内容为空'}, ensure_ascii=False, indent=2))
            sys.exit(1)
            
    except Exception as e:
        print(json.dumps({
            'success': False,
            'message': f'输入处理错误: {str(e)}',
            'error': str(e)
        }, ensure_ascii=False, indent=2))
        sys.exit(1)
    
    # 进行语音合成
    result = synthesize_speech(
        text=text,
        voice=args.voice,
        format=args.format,
        sample_rate=args.sample_rate,
        volume=args.volume,
        rate=args.rate,
        pitch=args.pitch,
        output_file=args.output
    )
    
    # 输出结果
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 设置退出码
    sys.exit(0 if result['success'] else 1)

if __name__ == '__main__':
    main()