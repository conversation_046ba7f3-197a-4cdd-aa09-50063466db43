<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        textarea, select, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 120px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        audio {
            width: 100%;
            margin-top: 10px;
        }
        .loading {
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎙️ 语音合成测试</h1>
        
        <div class="form-group">
            <label for="text">输入文本：</label>
            <textarea id="text" placeholder="请输入要转换为语音的文本...">你好，世界！这是一个语音合成测试。</textarea>
        </div>
        
        <div class="form-group">
            <label for="voice">选择音色：</label>
            <select id="voice">
                <option value="longxiaochun">龙小淳 (知性女声)</option>
                <option value="longxiaoxia">龙小夏 (清新女声)</option>
                <option value="longxiu">龙修 (磁性男声)</option>
                <option value="longmiao">龙妙 (甜美女声)</option>
                <option value="longyue">龙悦 (优雅女声)</option>
                <option value="longnan">龙楠 (成熟男声)</option>
                <option value="longxiaochun_v2">龙小淳V2 (支持SSML)</option>
                <option value="yumi_v2">YUMI V2 (支持SSML)</option>
            </select>
        </div>
        
        <button id="synthesizeBtn" onclick="synthesize()">🎙️ 开始合成</button>
        
        <div id="result" class="result">
            <div id="message"></div>
            <audio id="audioPlayer" controls style="display: none;"></audio>
        </div>
    </div>

    <script>
        async function synthesize() {
            const textInput = document.getElementById('text');
            const voiceSelect = document.getElementById('voice');
            const synthesizeBtn = document.getElementById('synthesizeBtn');
            const result = document.getElementById('result');
            const message = document.getElementById('message');
            const audioPlayer = document.getElementById('audioPlayer');
            
            const text = textInput.value.trim();
            const voice = voiceSelect.value;
            
            if (!text) {
                showResult('请输入要合成的文本', 'error');
                return;
            }
            
            // 禁用按钮，显示加载状态
            synthesizeBtn.disabled = true;
            synthesizeBtn.textContent = '🔄 合成中...';
            showResult('正在合成语音，请稍候...', 'loading');
            
            try {
                const response = await fetch('/api/tts/synthesize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        voice: voice,
                        format: 'mp3',
                        saveFile: true
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showResult(`✅ 语音合成成功！文件大小: ${(data.data.size / 1024).toFixed(1)} KB`, 'success');
                    
                    // 设置音频播放器
                    audioPlayer.src = data.data.audioUrl;
                    audioPlayer.style.display = 'block';
                    audioPlayer.load();
                    
                    console.log('合成结果:', data);
                } else {
                    showResult(`❌ 合成失败: ${data.message}`, 'error');
                }
                
            } catch (error) {
                console.error('请求失败:', error);
                showResult(`❌ 请求失败: ${error.message}`, 'error');
            } finally {
                // 恢复按钮状态
                synthesizeBtn.disabled = false;
                synthesizeBtn.textContent = '🎙️ 开始合成';
            }
        }
        
        function showResult(text, type) {
            const result = document.getElementById('result');
            const message = document.getElementById('message');
            
            message.textContent = text;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }
        
        // 页面加载完成后测试API连接
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/api/tts/voices');
                const data = await response.json();
                
                if (data.success) {
                    console.log('✅ API连接正常，可用音色数量:', data.count);
                } else {
                    console.error('❌ API连接失败:', data.message);
                }
            } catch (error) {
                console.error('❌ 无法连接到API:', error);
            }
        });
    </script>
</body>
</html>
