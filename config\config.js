const dotenv = require('dotenv');
dotenv.config();

module.exports = {
  // 阿里云DashScope配置
  dashscope: {
    apiKey: process.env.DASHSCOPE_API_KEY,
    websocketUrl: 'wss://dashscope.aliyuncs.com/api-ws/v1/inference',
    model: 'cosyvoice-v2'
  },

  // 服务器配置
  server: {
    port: process.env.PORT || 3001,
    env: process.env.NODE_ENV || 'development'
  },

  // 音频配置
  audio: {
    outputDir: process.env.AUDIO_OUTPUT_DIR || './public/audio',
    defaultFormat: 'mp3',
    defaultSampleRate: 22050,
    defaultVolume: 50,
    defaultRate: 1.0,
    defaultPitch: 1.0
  },

  // 可用的音色配置
  voices: [
    
    // 2. CosyVoice v1基础音色(6个)
    {
      id: 'longxiaochun',
      name: '龙小淳',
      description: '知性女声，优雅大方',
      gender: 'female',
      language: 'zh-CN',
      category: 'basic_v1',
      supportsSSML: false
    },
    {
      id: 'longxiaoxia',
      name: '龙小夏',
      description: '清新女声，活泼可爱',
      gender: 'female',
      language: 'zh-CN',
      category: 'basic_v1',
      supportsSSML: false
    },
    {
      id: 'longxiu',
      name: '龙修',
      description: '磁性男声，沉稳有力',
      gender: 'male',
      language: 'zh-CN',
      category: 'basic_v1',
      supportsSSML: false
    },
    {
      id: 'longmiao',
      name: '龙妙',
      description: '甜美女声，温柔动听',
      gender: 'female',
      language: 'zh-CN',
      category: 'basic_v1',
      supportsSSML: false
    },
    {
      id: 'longyue',
      name: '龙悦',
      description: '优雅女声，端庄大气',
      gender: 'female',
      language: 'zh-CN',
      category: 'basic_v1',
      supportsSSML: false
    },
    {
      id: 'longnan',
      name: '龙楠',
      description: '成熟男声，稳重可靠',
      gender: 'male',
      language: 'zh-CN',
      category: 'basic_v1',
      supportsSSML: false
    },
    
    // 3. 影视配音音色(4个)
    {
      id: 'longfei',
      name: '龙飞',
      description: '新闻播报，专业权威',
      gender: 'male',
      language: 'zh-CN',
      category: 'media',
      supportsSSML: false
    },
    {
      id: 'longshuo',
      name: '龙说',
      description: '导航播报，清晰明了',
      gender: 'female',
      language: 'zh-CN',
      category: 'media',
      supportsSSML: false
    },
    {
      id: 'longlang',
      name: '龙朗',
      description: '有声读物，富有感情',
      gender: 'male',
      language: 'zh-CN',
      category: 'media',
      supportsSSML: false
    },
    {
      id: 'longqin',
      name: '龙琴',
      description: '影视配音，表现力强',
      gender: 'female',
      language: 'zh-CN',
      category: 'media',
      supportsSSML: false
    },
    
    // 4. 特色音色(4个)
    {
      id: 'longjielidou',
      name: '龙杰力豆',
      description: '童声，纯真可爱',
      gender: 'child',
      language: 'zh-CN',
      category: 'special',
      supportsSSML: false
    },
    {
      id: 'longlaotie',
      name: '龙老铁',
      description: '东北话，幽默风趣',
      gender: 'male',
      language: 'zh-CN-northeast',
      category: 'special',
      supportsSSML: false
    },
    {
      id: 'longxiaobai',
      name: '龙小白',
      description: '智能助手，贴心服务',
      gender: 'female',
      language: 'zh-CN',
      category: 'special',
      supportsSSML: false
    },
    {
      id: 'longsitela',
      name: '龙思特拉',
      description: '客服专用，专业耐心',
      gender: 'female',
      language: 'zh-CN',
      category: 'special',
      supportsSSML: false
    },
    
    // 5. CosyVoice v2高级音色(4个) - 支持SSML
    {
      id: 'longxiaochun_v2',
      name: '龙小淳V2',
      description: '知性女声，支持SSML标记',
      gender: 'female',
      language: 'zh-CN',
      category: 'advanced_v2',
      supportsSSML: true
    },
    {
      id: 'yumi_v2',
      name: 'YUMI V2',
      description: '温暖女声，支持SSML标记',
      gender: 'female',
      language: 'zh-CN',
      category: 'advanced_v2',
      supportsSSML: true
    },
    {
      id: 'longxiu_v2',
      name: '龙修V2',
      description: '磁性男声，支持SSML标记',
      gender: 'male',
      language: 'zh-CN',
      category: 'advanced_v2',
      supportsSSML: true
    },
    {
      id: 'longsanshu',
      name: '龙三叔',
      description: '成熟男声，支持SSML标记',
      gender: 'male',
      language: 'zh-CN',
      category: 'advanced_v2',
      supportsSSML: true
    }
  ]
};