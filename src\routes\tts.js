const express = require('express');
const path = require('path');
const ttsService = require('../services/ttsService');
const config = require('../../config/config');

const router = express.Router();

/**
 * 获取音色列表
 */
router.get('/voices', (req, res) => {
  try {
    const voices = ttsService.getVoices();
    res.json({
      success: true,
      data: voices,
      count: voices.length
    });
  } catch (error) {
    console.error('获取音色列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取音色列表失败',
      error: error.message
    });
  }
});

/**
 * 获取按分类组织的音色列表
 */
router.get('/voices/categories', (req, res) => {
  try {
    const categorizedVoices = ttsService.getVoicesByCategory();
    res.json({
      success: true,
      data: categorizedVoices
    });
  } catch (error) {
    console.error('获取分类音色列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取分类音色列表失败',
      error: error.message
    });
  }
});

/**
 * 语音合成接口
 */
router.post('/synthesize', async (req, res) => {
  try {
    const {
      text,
      voice = 'longxiaochun_v2',
      format = 'mp3',
      sampleRate = 22050,
      volume = 50,
      rate = 1.0,
      pitch = 1.0,
      saveFile = true
    } = req.body;

    // 参数验证
    if (!text || text.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: '请输入要合成的文本'
      });
    }

    // 验证音色是否存在
    const voices = ttsService.getVoices();
    const selectedVoice = voices.find(v => v.id === voice);
    if (!selectedVoice) {
      return res.status(400).json({
        success: false,
        message: '选择的音色不存在'
      });
    }

    // 验证参数范围
    if (volume < 0 || volume > 100) {
      return res.status(400).json({
        success: false,
        message: '音量参数应在0-100之间'
      });
    }

    if (rate < 0.5 || rate > 2.0) {
      return res.status(400).json({
        success: false,
        message: '语速参数应在0.5-2.0之间'
      });
    }

    if (pitch < 0.5 || pitch > 2.0) {
      return res.status(400).json({
        success: false,
        message: '音调参数应在0.5-2.0之间'
      });
    }

    console.log(`🎙️  开始语音合成：[${selectedVoice.name}] "${text.substring(0, 50)}..."`);

    // 执行语音合成
    const audioData = await ttsService.synthesize({
      text: text.trim(),
      voice,
      format,
      sampleRate,
      volume,
      rate,
      pitch
    });

    if (saveFile) {
      // 保存文件并返回文件信息
      const fileName = await ttsService.saveAudioFile(audioData, format);
      res.json({
        success: true,
        message: '语音合成成功',
        data: {
          audioUrl: `/audio/${fileName}`,
          fileName: fileName,
          size: audioData.length,
          format: format,
          voice: selectedVoice,
          parameters: {
            sampleRate,
            volume,
            rate,
            pitch
          }
        }
      });
    } else {
      // 直接返回音频数据
      res.set({
        'Content-Type': format === 'mp3' ? 'audio/mpeg' : 'audio/wav',
        'Content-Length': audioData.length,
        'Content-Disposition': `attachment; filename="synthesis_${Date.now()}.${format}"`
      });
      res.send(audioData);
    }

  } catch (error) {
    console.error('语音合成失败:', error);
    res.status(500).json({
      success: false,
      message: '语音合成失败',
      error: error.message
    });
  }
});

/**
 * 下载音频文件
 */
router.get('/download/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(config.audio.outputDir, filename);
    
    // 安全检查：防止路径遍历攻击
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        success: false,
        message: '非法的文件名'
      });
    }

    res.download(filePath, (err) => {
      if (err) {
        console.error('文件下载失败:', err);
        res.status(404).json({
          success: false,
          message: '文件不存在或已被删除'
        });
      }
    });

  } catch (error) {
    console.error('下载音频文件失败:', error);
    res.status(500).json({
      success: false,
      message: '下载失败',
      error: error.message
    });
  }
});

/**
 * 获取系统配置信息
 */
router.get('/config', (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        supportedFormats: ['mp3', 'wav'],
        sampleRates: [16000, 22050, 44100],
        volumeRange: { min: 0, max: 100, default: 50 },
        rateRange: { min: 0.5, max: 2.0, default: 1.0 },
        pitchRange: { min: 0.5, max: 2.0, default: 1.0 },
        maxTextLength: 2000,
        model: config.dashscope.model
      }
    });
  } catch (error) {
    console.error('获取配置信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取配置信息失败',
      error: error.message
    });
  }
});

module.exports = router;