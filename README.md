# 🎙️ 阿里云CosyVoice文本转语音系统

基于阿里云DashScope CosyVoice-v2技术的高质量文本转语音系统，支持多种音色和参数调节。

## ✅ 项目状态

**项目已修复并正常工作！**

主要解决的问题：
- ✅ 修复了Python脚本输出格式问题（调试信息输出到stderr）
- ✅ 修复了Node.js与Python脚本的通信问题
- ✅ 修复了文本编码处理问题
- ✅ 完善了错误处理和日志输出

## 🧪 测试结果

- ✅ Python直接调用阿里云API：成功
- ✅ Node.js调用Python脚本：成功
- ✅ 完整的API接口：成功
- ✅ 前端页面：正常工作

## ✨ 功能特性

### 🎵 音色支持（共22个音色）

- **标准音色（4个）**：
  - 智楚：标准女声，清晰自然
  - 智小夏：活泼女声，青春洋溢
  - 智小源：温暖男声，亲切友善
  - 智喵：可爱女声，萌萌哒

- **CosyVoice v1基础音色（6个）**：
  - 龙小淳：知性女声，优雅大方
  - 龙小夏：清新女声，活泼可爱
  - 龙修：磁性男声，沉稳有力
  - 龙妙：甜美女声，温柔动听
  - 龙悦：优雅女声，端庄大气
  - 龙楠：成熟男声，稳重可靠

- **影视配音音色（4个）**：
  - 龙飞：新闻播报，专业权威
  - 龙说：导航播报，清晰明了
  - 龙朗：有声读物，富有感情
  - 龙琴：影视配音，表现力强

- **特色音色（4个）**：
  - 龙杰力豆：童声，纯真可爱
  - 龙老铁：东北话，幽默风趣
  - 龙小白：智能助手，贴心服务
  - 龙思特拉：客服专用，专业耐心

- **CosyVoice v2高级音色（4个，支持SSML）**：
  - 龙小淳V2：知性女声，支持SSML标记
  - YUMI V2：温暖女声，支持SSML标记
  - 龙修V2：磁性男声，支持SSML标记
  - 龙三叔：成熟男声，支持SSML标记

### 🎛️ 参数调节
- **音频格式**：MP3、WAV
- **音量调节**：0-100
- **语速调节**：0.5x-2.0x
- **音调调节**：0.5x-2.0x
- **采样率**：16000Hz、22050Hz、44100Hz

### 🚀 核心功能
- ✅ 实时语音合成
- ✅ 音色分类筛选
- ✅ 参数自由调节
- ✅ 音频在线预览
- ✅ 音频文件下载
- ✅ 响应式界面设计
- ✅ 字符数实时统计
- ✅ 错误处理和状态提示

## 🛠️ 技术栈

### 后端
- **Node.js** + **Express** - 服务器框架
- **WebSocket** - 与阿里云DashScope实时通信
- **dotenv** - 环境变量管理
- **cors** - 跨域资源共享
- **fs-extra** - 文件系统操作

### 前端
- **原生JavaScript** - 用户交互逻辑
- **HTML5** + **CSS3** - 响应式界面
- **Fetch API** - HTTP请求
- **Web Audio API** - 音频播放

### 第三方服务
- **阿里云DashScope** - CosyVoice-v2语音合成服务

## 🚀 快速开始

### 1. 环境要求
- Node.js >= 14.0.0
- npm >= 6.0.0
- 阿里云DashScope API密钥

### 2. 安装依赖
\`\`\`bash
npm install
\`\`\`

### 3. 配置环境变量
编辑 `.env` 文件，配置您的API密钥：
\`\`\`env
# 阿里云API配置
DASHSCOPE_API_KEY=your_api_key_here

# 服务器配置
PORT=3001
NODE_ENV=development

# 音频输出目录
AUDIO_OUTPUT_DIR=./public/audio
\`\`\`

### 4. 启动服务
\`\`\`bash
npm start
\`\`\`

### 5. 访问应用
打开浏览器访问：`http://localhost:3000`

### 6. 快速测试
访问 `http://localhost:3000/test.html` 可以使用简化的测试页面。

## 📖 使用指南

### 基本操作流程
1. **输入文本**：在文本输入框中输入要转换的文本（最多2000字符）
2. **选择音色**：从有声书或社交陪伴分类中选择合适的音色
3. **调节参数**：根据需要调整音量、语速、音调等参数
4. **开始合成**：点击"开始合成"按钮进行语音转换
5. **预览播放**：合成完成后可在线预览音频效果
6. **下载保存**：点击"下载音频"按钮保存音频文件

### 参数说明
- **音量**：控制音频的响度，范围0-100
- **语速**：控制语音的快慢，范围0.5x-2.0x（1.0为正常语速）
- **音调**：控制音高，范围0.5x-2.0x（1.0为正常音调）
- **格式**：输出音频格式，支持MP3和WAV

### 字符计算规则
按照阿里云DashScope规则：
- 汉字：每个字符计为2个字符
- 英文字母/数字/标点/空格：每个计为1个字符
- 单次合成限制：2000字符

## 🔧 API接口

### 获取音色列表
\`\`\`
GET /api/tts/voices
\`\`\`

### 获取分类音色
\`\`\`
GET /api/tts/voices/categories
\`\`\`

### 语音合成
\`\`\`
POST /api/tts/synthesize
Content-Type: application/json

{
  "text": "要合成的文本",
  "voice": "longxiaochun_v2",
  "format": "mp3",
  "volume": 50,
  "rate": 1.0,
  "pitch": 1.0
}
\`\`\`

### 下载音频
\`\`\`
GET /api/tts/download/:filename
\`\`\`

## 📁 项目结构

\`\`\`
txt-speech/
├── config/
│   └── config.js          # 配置文件
├── src/
│   ├── routes/
│   │   └── tts.js          # API路由
│   └── services/
│       └── ttsService.js   # TTS服务
├── public/
│   ├── audio/              # 音频文件存储
│   ├── css/
│   │   └── style.css       # 样式文件
│   ├── js/
│   │   └── app.js          # 前端逻辑
│   └── index.html          # 主页面
├── .env                    # 环境变量
├── package.json            # 项目配置
├── server.js               # 服务器入口
└── README.md               # 项目说明
\`\`\`

## 🔐 安全说明

- API密钥存储在环境变量中，不要提交到代码仓库
- 服务器验证所有输入参数，防止恶意请求
- 文件下载使用安全路径检查，防止路径遍历攻击
- 建议在生产环境中使用HTTPS协议

## 🚨 错误处理

系统包含完善的错误处理机制：
- WebSocket连接错误自动重试
- API请求超时处理
- 音频格式验证
- 用户友好的错误提示

## 📊 性能优化

- 音频文件异步处理，提高响应速度
- 前端缓存配置信息，减少重复请求
- 响应式设计，适配各种设备尺寸
- 音频流式传输，支持大文件处理

## 🆘 常见问题

### 1. API密钥配置错误
确保在 `.env` 文件中正确配置了 `DASHSCOPE_API_KEY`

### 2. 音频合成失败
- 检查网络连接
- 确认API密钥有效
- 检查文本是否符合字符限制

### 3. 音频播放问题
- 确认浏览器支持HTML5音频
- 检查音频文件是否存在
- 尝试不同的音频格式

### 4. 端口占用
如果3001端口被占用，可在 `.env` 文件中修改 `PORT` 配置

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

🎵 享受高质量的语音合成体验！