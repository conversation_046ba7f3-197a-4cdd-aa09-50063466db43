#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from dashscope.audio.tts_v2 import SpeechSynthesizer

# 设置API密钥
os.environ['DASHSCOPE_API_KEY'] = 'sk-b4b5b8b8b8b8b8b8b8b8b8b8b8b8b8b8'

def test_chinese_tts():
    try:
        # 直接硬编码中文文本
        text = "你好，世界"
        print(f"测试文本: {text}")
        print(f"文本编码: {text.encode('utf-8')}")
        
        # 创建语音合成器
        synthesizer = SpeechSynthesizer(
            model='cosyvoice-v1',
            voice='longxiaochun'
        )
        
        # 进行语音合成
        print("开始语音合成...")
        audio_data = synthesizer.call(text)
        
        if audio_data is None:
            print('语音合成失败，返回数据为空')
            return False
        
        # 保存音频文件
        with open('test_chinese_direct.mp3', 'wb') as f:
            f.write(audio_data)
        
        print(f'语音合成成功，文件大小: {len(audio_data)} 字节')
        return True
        
    except Exception as e:
        print(f'语音合成失败: {str(e)}')
        return False

if __name__ == '__main__':
    test_chinese_tts()