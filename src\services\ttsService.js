const { spawn } = require('child_process');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs-extra');
const path = require('path');
const config = require('../../config/config');

class TTSService {
  constructor() {
    this.apiKey = config.dashscope.apiKey;
    this.model = config.dashscope.model;
    this.pythonScript = path.join(__dirname, 'tts_python.py');
  }

  /**
   * 验证文本长度
   * @param {string} text 待合成文本
   * @returns {boolean} 是否符合长度要求
   */
  validateTextLength(text) {
    // 按阿里云规则计算字符数：汉字2字符，英文/数字/标点/空格1字符
    let charCount = 0;
    for (let char of text) {
      if (/[\u4e00-\u9fa5]/.test(char)) {
        charCount += 2; // 汉字
      } else {
        charCount += 1; // 英文字母/数字/标点/空格
      }
    }
    return charCount <= 2000; // 单次限制2000字符
  }

  /**
   * 语音合成
   * @param {Object} options 合成参数
   * @returns {Promise<Buffer>} 音频数据
   */
  async synthesize(options) {
    const {
      text,
      voice = 'longxiaochun_v2',
      format = 'mp3',
      sampleRate = 22050,
      volume = 50,
      rate = 1.0,
      pitch = 1.0
    } = options;

    // 验证文本长度
    if (!this.validateTextLength(text)) {
      throw new Error('文本长度超过限制（2000字符）');
    }

    // 验证API密钥
    if (!this.apiKey) {
      throw new Error('未配置DashScope API密钥');
    }

    return new Promise((resolve, reject) => {
      // 生成临时输出文件路径
      const fileName = `${Date.now()}_${uuidv4().slice(0, 8)}.${format}`;
      const outputPath = path.join(config.audio.outputDir, fileName);

      // 构建Python脚本参数（不包含text，通过stdin传递）
      const args = [
        this.pythonScript,
        '--voice', voice,
        '--format', format,
        '--sample-rate', sampleRate.toString(),
        '--volume', volume.toString(),
        '--rate', rate.toString(),
        '--pitch', pitch.toString(),
        '--output', outputPath
      ];

      // 创建临时文件来传递文本数据
      const tempFile = path.join(__dirname, '../../temp', `text_${Date.now()}.json`);
      
      // 确保临时目录存在
      const tempDir = path.dirname(tempFile);
      await fs.ensureDir(tempDir);
      
      // 写入文本到临时文件
      await fs.writeFile(tempFile, JSON.stringify({ text: text }), 'utf8');
      console.log(`📝 文本已写入临时文件: ${tempFile}`);
      
      // 添加临时文件参数
      args.push('--input-file', tempFile);

      console.log('🐍 调用Python脚本进行语音合成...');
      console.log('📝 文本:', text.substring(0, 50) + (text.length > 50 ? '...' : ''));
      console.log('🎤 音色:', voice);

      // 设置环境变量
      const env = { ...process.env };
      if (this.apiKey) {
        env.DASHSCOPE_API_KEY = this.apiKey;
      }

      // 执行Python脚本
      const python = spawn('python', args, {
        env: {
          ...env,
          PYTHONIOENCODING: 'utf-8'
        }
      });

      let stdout = '';
      let stderr = '';

      python.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      python.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      python.on('close', async (code) => {
        try {
          if (code === 0) {
            // 解析Python脚本的输出
            const result = JSON.parse(stdout);
            
            if (result.success) {
              // 读取生成的音频文件
              const audioData = await fs.readFile(outputPath);
              console.log(`🎶 语音合成成功，文件大小: ${audioData.length} 字节`);
              console.log(`📊 请求ID: ${result.request_id}`);
              console.log(`⏱️ 首包延迟: ${result.first_package_delay}ms`);
              resolve(audioData);
            } else {
              reject(new Error(result.message || '语音合成失败'));
            }
          } else {
            console.error('❌ Python脚本执行失败');
            console.error('📤 stdout:', stdout);
            console.error('📤 stderr:', stderr);
            reject(new Error(`Python脚本执行失败 (退出码: ${code}): ${stderr || stdout}`));
          }
        } catch (err) {
          console.error('❌ 处理Python脚本输出时出错:', err);
          reject(new Error(`处理语音合成结果失败: ${err.message}`));
        } finally {
          // 清理临时文件
          try {
            if (await fs.pathExists(outputPath)) {
              await fs.unlink(outputPath);
            }
            if (await fs.pathExists(tempFile)) {
              await fs.unlink(tempFile);
              console.log(`🗑️ 临时文件已清理: ${tempFile}`);
            }
          } catch (cleanupErr) {
            console.warn('⚠️ 清理临时文件失败:', cleanupErr.message);
          }
        }
      });

      python.on('error', (err) => {
        console.error('❌ 启动Python脚本失败:', err);
        reject(new Error(`启动Python脚本失败: ${err.message}`));
      });

      // 设置超时
      setTimeout(() => {
        python.kill('SIGTERM');
        reject(new Error('语音合成超时'));
      }, 60000); // 60秒超时
    });
  }

  /**
   * 保存音频文件
   * @param {Buffer} audioData 音频数据
   * @param {string} format 音频格式
   * @returns {Promise<string>} 文件路径
   */
  async saveAudioFile(audioData, format = 'mp3') {
    const fileName = `${Date.now()}_${uuidv4().slice(0, 8)}.${format}`;
    const filePath = path.join(config.audio.outputDir, fileName);
    
    await fs.writeFile(filePath, audioData);
    console.log(`💾 音频文件已保存: ${filePath}`);
    
    return fileName; // 返回文件名，用于前端访问
  }

  /**
   * 获取支持的音色列表
   * @returns {Array} 音色列表
   */
  getVoices() {
    return config.voices;
  }

  /**
   * 获取音色分类
   * @returns {Object} 按分类组织的音色
   */
  getVoicesByCategory() {
    const categorized = {
      audiobook: [],
      companion: []
    };

    config.voices.forEach(voice => {
      if (categorized[voice.category]) {
        categorized[voice.category].push(voice);
      }
    });

    return categorized;
  }
}

module.exports = new TTSService();