{"name": "txt-speech-system", "version": "1.0.0", "description": "阿里云CosyVoice文本转语音系统", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["tts", "text-to-speech", "alibaba", "cosyvoice", "dashscope"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "uuid": "^9.0.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "fs-extra": "^11.1.1"}, "devDependencies": {"nodemon": "^3.0.1"}}